[versions]
agp = "8.11.0"
kotlin = "2.2.0"
ksp = "2.2.0-2.0.2"
javaVersion = "17"
coreKtx = "1.16.0"
lifecycleRuntimeKtx = "2.9.1"
activityCompose = "1.10.1"
composeBom = "2025.06.01"
composeCompiler = "1.5.14"
hiltAndroid = "2.56.2"
retrofit = "2.9.0"
kotlinxSerializationJson = "1.9.0"
junit = "4.13.2"
junitVersion = "1.2.1"
mockk = "1.14.4"
coroutines = "1.10.2"
detekt = "1.23.8"
detektCompose = "0.4.22"

compileSdk = "34"
targetSdk = "34"
minSdk = "24"
versionCode = "1"
versionName = "1.0"

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }

# Compose BOM & UI
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }

# Dependency Injection
hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "hiltAndroid" }
hilt-android-compiler = { module = "com.google.dagger:hilt-android-compiler", version.ref = "hiltAndroid" }
hilt-android-test = { module = "com.google.dagger:hilt-android-testing", version.ref = "hiltAndroid" }

# Networking
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofit" }
retrofit-converter-kotlinx = { module = "com.jakewharton.retrofit:retrofit2-kotlinx-serialization-converter", version = "1.0.0" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerializationJson" }

# Testing
test-junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
test-mockk = { module = "io.mockk:mockk", version.ref = "mockk" }
test-coroutines = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test", version.ref = "coroutines" }

# Code Quality
detekt-compose = { module = "io.nlopez.compose.rules:detekt", version.ref = "detektCompose" }

[plugins]
# Build tools
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }

# Dependency Injection
hilt-android = { id = "com.google.dagger.hilt.android", version.ref = "hiltAndroid" }

# Code Quality
detekt = { id = "io.gitlab.arturbosch.detekt", version.ref = "detekt" }
