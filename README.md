# URL Shortener Android App

A simple URL shortener app built with Jetpack Compose and Kotlin.

<img src="apk_screenshot.png" alt="URL Shortener App Screenshot" width="500"/>

## Features

- Convert long URLs into short
- View and manage previously shortened URLs

## Tech Stack

- **Kotlin 2.0** + **Jetpack Compose**
- **MVI Architecture** with StateFlow
- **Retrofit** for networking
- **Dagger Hilt** for dependency injection
- **Detekt** for code analysis

## Getting Started

### Requirements
- Android Studio Meerkat (2024.3.1) or newer
- JDK 17+
- Android SDK 34

### Build & Run
```bash
# Run tests
./gradlew test

# Build debug APK
./gradlew assembleDebug

# Code analysis
./gradlew detekt
```
